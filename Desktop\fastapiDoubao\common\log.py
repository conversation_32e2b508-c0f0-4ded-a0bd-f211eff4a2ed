import logging
import sys
from datetime import datetime

# 创建日志格式器
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 创建控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.DEBUG)
console_handler.setFormatter(formatter)

# 创建文件处理器
file_handler = logging.FileHandler('doubao_api.log', encoding='utf-8')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(formatter)

# 创建主logger
logger = logging.getLogger('doubao')
logger.setLevel(logging.DEBUG)

# 避免重复添加处理器
if not logger.handlers:
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

# 防止日志向上传播到根logger
logger.propagate = False

def get_logger(name):
    """获取指定名称的logger"""
    return logging.getLogger(f'doubao.{name}')
